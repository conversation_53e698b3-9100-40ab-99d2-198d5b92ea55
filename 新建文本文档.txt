O:\ComfyUI-V3.0>.\python_embeded\python.exe -s ComfyUI\main.py --windows-standalone-build --enable-cors-header *
[START] Security scan
[DONE] Security scan

ComfyUI-Manager: installing dependencies done.

** ComfyUI startup time: 2025-07-01 19:20:18.506
** Platform: Windows
** Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
** Python executable: O:\ComfyUI-V3.0\python_embeded\python.exe
** ComfyUI Path: O:\ComfyUI-V3.0\ComfyUI
** ComfyUI Base Folder Path: O:\ComfyUI-V3.0\ComfyUI
** User directory: O:\ComfyUI-V3.0\ComfyUI\user
** ComfyUI-Manager config path: O:\ComfyUI-V3.0\ComfyUI\user\default\ComfyUI-Manager\config.ini
** Log path: O:\ComfyUI-V3.0\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\rgthree-comfy
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Easy-Use
14.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Manager

Checkpoint files will always be loaded safely.
Total VRAM 10240 MB, total RAM 32694 MB
pytorch version: 2.6.0+cu126
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 3080 : cudaMallocAsync
Using pytorch attention
Python version: 3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit (AMD64)]
ComfyUI version: 0.3.43
ComfyUI frontend version: 1.23.4
[Prompt Server] web root: O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\comfyui_frontend_package\static
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\comfy_api_nodes\canary.py", line 5, in <module>
raise Exception("INSTALL NEW VERSION OF PYAV TO USE API NODES.")
Exception: INSTALL NEW VERSION OF PYAV TO USE API NODES.

Cannot import O:\ComfyUI-V3.0\ComfyUI\comfy_api_nodes\canary.py module for custom nodes: INSTALL NEW VERSION OF PYAV TO USE API NODES.
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\bringing-old-photos-back-to-life_init.py", line 1, in <module>
from .nodes import BOPBTL_ScratchMask, BOPBTL_LoadScratchMaskModel, BOPBTL_LoadRestoreOldPhotosModel, BOPBTL_RestoreOldPhotos, BOPBTL_LoadFaceDetectorModel, BOPBTL_DetectFaces, BOPBTL_LoadFaceEnhancerModel, BOPBTL_EnhanceFaces, BOPBTL_EnhanceFacesAdvanced, BOPBTL_BlendFaces, BOPBTL_DetectEnhanceBlendFaces
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\bringing-old-photos-back-to-life\nodes.py", line 17, in <module>
from .Face_Detection import detect_all_dlib as FaceDetector
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\bringing-old-photos-back-to-life\Face_Detection\detect_all_dlib.py", line 15, in <module>
import dlib
ModuleNotFoundError: No module named 'dlib'

Cannot import O:\ComfyUI-V3.0\ComfyUI\custom_nodes\bringing-old-photos-back-to-life module for custom nodes: No module named 'dlib'
ComfyUI-GGUF: Partial torch compile only, consider updating pytorch
O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\timm\models\layers_init_.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers
warnings.warn(f"Importing from {name} is deprecated, please import via timm.layers", FutureWarning)
O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\pydub\utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
Using wildcards dir:O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Apt_Preset\wildcards or O:\ComfyUI-V3.0\ComfyUI\wildcards
[Crystools INFO] Crystools version: 1.25.1
[Crystools INFO] Platform release: 10
[Crystools INFO] JETSON: Not detected.
[Crystools INFO] CPU: AMD Ryzen 5 5600X 6-Core Processor - Arch: AMD64 - OS: Windows 10
[Crystools INFO] pynvml (NVIDIA) initialized.
[Crystools INFO] GPU/s:
[Crystools INFO] 0) NVIDIA GeForce RTX 3080
[Crystools INFO] NVIDIA Driver: 576.80
[ComfyUI-Easy-Use] server: v1.3.1 Loaded
[ComfyUI-Easy-Use] web root: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Easy-Use\web_version/v2 Loaded
ComfyUI-GGUF: Partial torch compile only, consider updating pytorch
Total VRAM 10240 MB, total RAM 32694 MB
pytorch version: 2.6.0+cu126
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 3080 : cudaMallocAsync

Loading: ComfyUI-Impact-Pack (V8.12)

[Impact Pack] Wildcards loading done.

Loading: ComfyUI-Impact-Subpack (V1.3.3)

[Impact Pack/Subpack] Using folder_paths to determine whitelist path: O:\ComfyUI-V3.0\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[Impact Pack/Subpack] Ensured whitelist directory exists: O:\ComfyUI-V3.0\ComfyUI\user\default\ComfyUI-Impact-Subpack
[Impact Pack/Subpack] Loaded 0 model(s) from whitelist: O:\ComfyUI-V3.0\ComfyUI\user\default\ComfyUI-Impact-Subpack\model-whitelist.txt
[Impact Subpack] ultralytics_bbox: O:\ComfyUI-V3.0\ComfyUI\models\ultralytics\bbox
[Impact Subpack] ultralytics_segm: O:\ComfyUI-V3.0\ComfyUI\models\ultralytics\segm

Loading: ComfyUI-Inspire-Pack (V1.20)

Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-Inspire-Pack_init.py", line 35, in <module>
imported_module = importlib.import_module(".inspire.{}".format(module_name), name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init_.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-Inspire-Pack\inspire\regional_nodes.py", line 7, in <module>
import webcolors
ModuleNotFoundError: No module named 'webcolors'

Cannot import O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-Inspire-Pack module for custom nodes: No module named 'webcolors'

Loading: ComfyUI-Manager (V3.31.12)

[ComfyUI-Manager] network_mode: public

ComfyUI Version: v0.3.43-7-g170c7bb9 | Released on '2025-06-29'
Mixlab Nodes: Loaded

ChatGPT.available False
edit_mask.available True
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json

clip_interrogator_model not found: O:\ComfyUI-V3.0\ComfyUI\models\clip_interrogator\Salesforce\blip-image-captioning-base, pls download from https://huggingface.co/Salesforce/blip-image-captioning-base
ClipInterrogator.available True
TextGenerateNode.available False
RembgNode_.available True
ffmpeg could not be found. Using ffmpeg from imageio-ffmpeg.
TripoSR.available
MiniCPMNode.available
Scenedetect.available False
FishSpeech.available False
SenseVoice.available False
Whisper.available False
fal-client## OK
FalVideo.available
======================================== ComfyUI-nunchaku Initialization ========================================
Nunchaku version: 0.2.0
ComfyUI-nunchaku version: 0.3.2
ComfyUI-nunchaku 0.3.2 is not compatible with nunchaku 0.2.0. Please update nunchaku to a supported version in ['v0.3.1'].
Node NunchakuFluxDiTLoader import failed:
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku_init_.py", line 31, in <module>
from .nodes.models.flux import NunchakuFluxDiTLoader
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\nodes\models\flux.py", line 17, in <module>
from ...wrappers.flux import ComfyFluxWrapper
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\wrappers\flux.py", line 12, in <module>
from nunchaku.pipeline.pipeline_flux_pulid import PuLIDPipeline
ModuleNotFoundError: No module named 'nunchaku.pipeline'
Node NunchakuFluxLoraLoader import failed:
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku_init_.py", line 38, in <module>
from .nodes.lora.flux import NunchakuFluxLoraLoader
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\nodes\lora\flux.py", line 8, in <module>
from ...wrappers.flux import ComfyFluxWrapper
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\wrappers\flux.py", line 12, in <module>
from nunchaku.pipeline.pipeline_flux_pulid import PuLIDPipeline
ModuleNotFoundError: No module named 'nunchaku.pipeline'
Nodes NunchakuPulidApply,NunchakuPulidLoader, NunchakuPuLIDLoaderV2 and NunchakuFluxPuLIDApplyV2 import failed:
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku_init_.py", line 61, in <module>
from .nodes.models.pulid import (
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\nodes\models\pulid.py", line 16, in <module>
from nunchaku.models.pulid.pulid_forward import pulid_forward
ModuleNotFoundError: No module named 'nunchaku.models.pulid'
Node NunchakuModelMerger import failed:
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku_init_.py", line 79, in <module>
from .nodes.tools.merge_safetensors import NunchakuModelMerger
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku\nodes\tools\merge_safetensors.py", line 6, in <module>
from nunchaku.merge_safetensors import merge_safetensors
ModuleNotFoundError: No module named 'nunchaku.merge_safetensors'

FETCH ComfyRegistry Data: 5/90

[ReActor] - STATUS - Running v0.6.1-b2 in ComfyUI
Torch version: 2.6.0+cu126
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1967, in get_module
return importlib.import_module("." + module_name, self.name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines_init.py", line 64, in <module>
from .document_question_answering import DocumentQuestionAnsweringPipeline
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\document_question_answering.py", line 29, in <module>
from .question_answering import select_starts_ends
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\question_answering.py", line 9, in <module>
from ..data import SquadExample, SquadFeatures, squad_convert_examples_to_features
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data_init.py", line 29, in <module>
from .processors import (
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors_init.py", line 15, in <module>
from .glue import glue_convert_examples_to_features, glue_output_modes, glue_processors, glue_tasks_num_labels
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors\glue.py", line 79, in <module>
examples: tf.data.Dataset,
^^^^^^^
AttributeError: module 'tensorflow' has no attribute 'data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor_init.py", line 23, in <module>
from .nodes import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor\nodes.py", line 66, in <module>
import scripts.reactor_sfw as sfw
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor\scripts\reactor_sfw.py", line 1, in <module>
from transformers import pipeline
File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1955, in getattr
module = self._get_module(self._class_to_module[name])
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1969, in _get_module
raise RuntimeError(
RuntimeError: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'

Cannot import O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor module for custom nodes: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'
No module named 'volcengine'
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-utils-nodes_init_.py", line 46, in <module>
imported_module = importlib.import_module(".py.{}".format(name), name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init_.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-utils-nodes\py\node_volcano.py", line 8, in <module>
from volcengine.visual.VisualService import VisualService
ModuleNotFoundError: No module named 'volcengine'
(pysssss:WD14Tagger) [DEBUG] Available ORT providers: TensorrtExecutionProvider, CUDAExecutionProvider, CPUExecutionProvider
(pysssss:WD14Tagger) [DEBUG] Using ORT providers: CUDAExecutionProvider, CPUExecutionProvider
Python version is above 3.10, patching the collections module.
Comfyroll Studio v1.76 :  175 Nodes Loaded
** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki

[O:\ComfyUI-V3_x_0\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts
[O:\ComfyUI-V3_x_0\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False
[O:\ComfyUI-V3_x_0\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider']
DWPose: Onnxruntime with acceleration providers detected
FETCH ComfyRegistry Data: 10/90
[DeepLXTranslateNode] Running server DeepLX...
FETCH ComfyRegistry Data: 15/90
[DeepLXTranslateNode] Checking DeepLX server. Attempt 10...
FETCH ComfyRegistry Data: 20/90
[DeepLXTranslateNode] Checking DeepLX server. Attempt 9...
DeepL X has been successfully launched! Listening on 0.0.0.0:1188
Developed <NAME_EMAIL> <NAME_EMAIL>.
[GIN] 2025/07/01 - 19:21:52 | 200 |            0s |       127.0.0.1 | GET      "/"
[DeepLXTranslateNode] Server responded successfully:  {"code":200,"message":"DeepL Free API, Developed by sjlleo and missuo. Go to /translate with POST. http://github.com/OwO-Network/DeepLX"}

[START] ComfyUI AlekPet Nodes v1.0.72

Node -> ArgosTranslateNode: ArgosTranslateCLIPTextEncodeNode, ArgosTranslateTextNode [Loading]
Node -> ChatGLMNode[Loading]
Node -> DeepLXTranslateNode[Loading]
Node -> DeepTranslatorNode: DeepTranslatorCLIPTextEncodeNode, DeepTranslatorTextNode [Loading]
Node -> ExtrasNode: PreviewTextNode, HexToHueNode, ColorsCorrectNode [Loading]
Node -> GoogleTranslateNode: GoogleTranslateCLIPTextEncodeNode, GoogleTranslateTextNode [Loading]
Node -> IDENode: IDENode [Loading]
Node -> PainterNode: PainterNode [Loading]
Node -> PoseNode: PoseNode [Loading]

[END] ComfyUI AlekPet Nodes

FizzleDorf Custom Nodes: Loaded
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1967, in get_module
return importlib.import_module("." + module_name, self.name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines_init.py", line 64, in <module>
from .document_question_answering import DocumentQuestionAnsweringPipeline
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\document_question_answering.py", line 29, in <module>
from .question_answering import select_starts_ends
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\question_answering.py", line 9, in <module>
from ..data import SquadExample, SquadFeatures, squad_convert_examples_to_features
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data_init.py", line 29, in <module>
from .processors import (
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors_init.py", line 15, in <module>
from .glue import glue_convert_examples_to_features, glue_output_modes, glue_processors, glue_tasks_num_labels
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors\glue.py", line 79, in <module>
examples: tf.data.Dataset,
^^^^^^^
AttributeError: module 'tensorflow' has no attribute 'data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_layerstyle_init.py", line 63, in <module>
imported_module = importlib.import_module(".py.{}".format(name), name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init_.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_layerstyle\py\phi_nodes.py", line 5, in <module>
from transformers import AutoModelForCausalLM, AutoProcessor, AutoTokenizer, pipeline
File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1955, in getattr
module = self._get_module(self._class_to_module[name])
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1969, in _get_module
raise RuntimeError(
RuntimeError: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'

Cannot import O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_layerstyle module for custom nodes: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1967, in get_module
return importlib.import_module("." + module_name, self.name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines_init.py", line 64, in <module>
from .document_question_answering import DocumentQuestionAnsweringPipeline
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\document_question_answering.py", line 29, in <module>
from .question_answering import select_starts_ends
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\pipelines\question_answering.py", line 9, in <module>
from ..data import SquadExample, SquadFeatures, squad_convert_examples_to_features
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data_init.py", line 29, in <module>
from .processors import (
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors_init.py", line 15, in <module>
from .glue import glue_convert_examples_to_features, glue_output_modes, glue_processors, glue_tasks_num_labels
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\data\processors\glue.py", line 79, in <module>
examples: tf.data.Dataset,
^^^^^^^
AttributeError: module 'tensorflow' has no attribute 'data'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
File "O:\ComfyUI-V3.0\ComfyUI\nodes.py", line 2124, in load_custom_node
module_spec.loader.exec_module(module)
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_LayerStyle_Advance_init.py", line 35, in <module>
imported_module = importlib.import_module(".py.{}".format(name), name)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "importlib_init_.py", line 90, in import_module
File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
File "<frozen importlib._bootstrap_external>", line 999, in exec_module
File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
File "O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_LayerStyle_Advance\py\phi_nodes.py", line 7, in <module>
from transformers import AutoModelForCausalLM, AutoProcessor, AutoTokenizer, pipeline
File "<frozen importlib._bootstrap>", line 1412, in _handle_fromlist
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1955, in getattr
module = self._get_module(self._class_to_module[name])
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\transformers\utils\import_utils.py", line 1969, in _get_module
raise RuntimeError(
RuntimeError: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'

Cannot import O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_LayerStyle_Advance module for custom nodes: Failed to import transformers.pipelines because of the following error (look up to see its traceback):
module 'tensorflow' has no attribute 'data'
pygame 2.6.1 (SDL 2.28.4, Python 3.12.9)
Hello from the pygame community. https://www.pygame.org/contribute.html
FETCH ComfyRegistry Data: 25/90

😺dzNodes: WordCloud ->  find 366 fonts in C:\Windows\fonts

FaceDetailer: Model directory already exists
FaceDetailer: Model already exists
Efficiency Nodes: Attempting to add Control Net options to the 'HiRes-Fix Script' Node (comfyui_controlnet_aux add-on)...Success!
WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: ffmpeg_bin_path is not set in O:\ComfyUI-V3.0\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894\was_suite_config.json config file. Will attempt to use system ffmpeg binaries if available.
FETCH ComfyRegistry Data: 30/90
WAS Node Suite: Finished. Loaded 220 nodes successfully.

Generated code
"Believe in yourself, take on your challenges, and dig deep within yourself to conquer fears." - Chantal Sutherland


Please 'pip install xformers'
Nvidia APEX normalization not installed, using PyTorch LayerNorm

[rgthree-comfy] Loaded 47 magnificent nodes. 🎉

WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: ffmpeg_bin_path is not set in O:\ComfyUI-V3.0\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json config file. Will attempt to use system ffmpeg binaries if available.
WAS Node Suite: Finished. Loaded 220 nodes successfully.

Generated code
"Art is the magic that brings beauty into the world." - Unknown
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

Import times for custom nodes:
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\websocket_image_save.py
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-get-meta
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-sampler-lcm-alternative
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_AdvancedRefluxControl
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-portrait-master-zh-cn
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_ipadapter_plus
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\masquerade-nodes-comfyui
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_InstantID
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_faceanalysis
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-lama-remover
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_Lama_Remover_Revived
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\AIGODLIKE-COMFYUI-TRANSLATION
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_slk_joy_caption_two
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\sd-dynamic-thresholding
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-WD14-Tagger
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_fizznodes
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-layerdiffuse
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor-node
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyMath
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_segment_anything
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_mienodes
0.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-dev-utils
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-HunyuanLoom
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-GGUF
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_CatVTON_Wrapper
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_ultimatesdupscale
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_essentials
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Impact-Subpack
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Custom-Scripts
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-FramePackWrapper
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-ic-light
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-frame-interpolation
0.1 seconds (IMPORT FAILED): O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-Inspire-Pack
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\rgthree-comfy
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-KJNodes
0.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-advanced-controlnet
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_caption_this
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-HunyuanVideoWrapper
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_Sonic
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-LTXVideo
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\efficiency-nodes-comfyui
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_wordcloud
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-animatediff-evolved
0.2 seconds (IMPORT FAILED): O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_LayerStyle_Advance
0.2 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-VideoHelperSuite
0.3 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\PuLID_ComfyUI
0.3 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Impact-Pack
0.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-nunchaku
0.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_controlnet_aux
0.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-ollama
0.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Crystools
0.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-cogvideoxwrapper
0.5 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-sam2
0.5 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-fluxtrainer
0.6 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-logicutils
0.6 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-AdvancedLivePortrait
0.8 seconds (IMPORT FAILED): O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_layerstyle
0.9 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-brushnet
1.0 seconds (IMPORT FAILED): O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-reactor
1.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\DZ-FaceDetailer
1.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-LivePortraitKJ
1.5 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI_RyanOnTheInside
1.6 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-utils-nodes
2.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Manager
2.6 seconds (IMPORT FAILED): O:\ComfyUI-V3.0\ComfyUI\custom_nodes\bringing-old-photos-back-to-life
3.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\was-node-suite-comfyui
4.4 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\pr-was-node-suite-comfyui-47064894
4.5 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Easy-Use
5.0 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes
13.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui_custom_nodes_alekpet
18.1 seconds: O:\ComfyUI-V3.0\ComfyUI\custom_nodes\ComfyUI-Apt_Preset

WARNING: some comfy_api_nodes/ nodes did not import correctly. This may be because they are missing some dependencies.

IMPORT FAILED: nodes_ideogram.py
IMPORT FAILED: nodes_openai.py
IMPORT FAILED: nodes_minimax.py
IMPORT FAILED: nodes_veo2.py
IMPORT FAILED: nodes_kling.py
IMPORT FAILED: nodes_bfl.py
IMPORT FAILED: nodes_luma.py
IMPORT FAILED: nodes_recraft.py
IMPORT FAILED: nodes_pixverse.py
IMPORT FAILED: nodes_stability.py
IMPORT FAILED: nodes_pika.py
IMPORT FAILED: nodes_runway.py
IMPORT FAILED: nodes_tripo.py
IMPORT FAILED: nodes_rodin.py
IMPORT FAILED: nodes_gemini.py

This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
Please run the update script: update/update_comfyui.bat

FETCH ComfyRegistry Data: 35/90
Context impl SQLiteImpl.
Will assume non-transactional DDL.
No target revision found.
Starting server

To see the GUI go to: http://127.0.0.1:8188
FETCH ComfyRegistry Data: 40/90
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/photoswipe-lightbox.esm.min.js
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/photoswipe.min.css
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/classic.min.css
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/juxtapose.css
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/model-viewer.min.js
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/pickr.min.js
O:\ComfyUI-V3.0\ComfyUI\custom_nodes\comfyui-mixlab-nodes\webApp\lib/juxtapose.min.js
FETCH ComfyRegistry Data: 45/90
FETCH ComfyRegistry Data: 50/90
Start Log Catchers...
[LogConsole] client [3490c9ccbf5644d0b41b5ca1025b2593], console [b7ccb84e-cdbb-4c56-9965-c4bb44ab5500], connected
Error handling request
Traceback (most recent call last):
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\aiohttp\web_protocol.py", line 477, in _handle_request
resp = await request_handler(request)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\aiohttp\web_app.py", line 567, in _handle
return await handler(request)
^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\python_embeded\Lib\site-packages\aiohttp\web_middlewares.py", line 117, in impl
return await handler(request)
^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\ComfyUI\server.py", line 52, in cache_control
response: web.Response = await handler(request)
^^^^^^^^^^^^^^^^^^^^^^
File "O:\ComfyUI-V3.0\ComfyUI\server.py", line 80, in cors_middleware
response.headers['Access-Control-Allow-Origin'] = allowed_origin
^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'headers'
出了什么问题